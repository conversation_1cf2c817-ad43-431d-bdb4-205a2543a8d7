# Quotelier Serverless Backend - AI Coding Agent Instructions

## Architecture Overview

This is a **microservices hospitality platform** built on AWS Lambda using Serverless Framework. The system manages booking requests, property integrations, and customer communications through ~18 independent services.

### Key Services & Boundaries
- **graphql**: Main API endpoint with <PERSON>W<PERSON> authentication and ACL
- **newRequest**: Request lifecycle management (create → send → pending → confirmed)
- **integrations**: External PMS connections (primarily Webhotelier)
- **properties**: Property data management with S3 storage
- **notifications**: Email communications via templates
- **workflows**: Step Functions for complex business processes
- **operators**: Operator/agent management and permissions

### Service Communication
Services communicate via **`@quotelier/invokeLambda`** package:
```javascript
const { invokeService } = require('@quotelier/invokeLambda');
// Pattern: invokeService(serviceName, functionName, payload)
await invokeService('integrations', 'getAvailability', { accountName, propertyCode });
```

## Critical Development Patterns

### Environment & Deployment
```bash
# Required AWS profile in ~/.aws/credentials
[quotelier] 
aws_access_key_id=xxx
aws_secret_access_key=xxx

# Stage-based deployment (avoid 'staging'/'production' in dev)
STAGE=your-name REGION=eu-central-1 npm run deploy
```

### Error Handling Convention
Every service follows identical error handling pattern in `/handlers/errorHandler.js`:
```javascript
function errorHandler(error, callback) {
  if (error instanceof CustomError) {
    callback(null, { success: false, response: null, error: { message: error.message, type: error.name } });
  } else {
    logger.error('errorHandler', error.message, error);
    callback(error, null);
  }
}
```

### Validation Pattern
All handlers use **Joi schemas** with consistent validation:
```javascript
const { validateEventSchema } = require('../lib/schemas');
validateEventSchema(event, eventSchema)
  .then(validatedData => /* process */)
  .catch(err => errorHandler(err, callback));
```

### Middleware Pattern
Every handler wraps with middleware initialization:
```javascript
module.exports = { handler: middlewares.init(handlerFunction) };
```

## Request State Machine
Requests follow strict state transitions managed in `services/newRequest/lib/request/actions.js`. Key states:
- **draft** → **pending** → **accepted** → **confirmed**
- State validation via `isStateAvailable()` before transitions
- Actions controlled by operator permissions in JWT

## Data Flow Specifics

### Property Data Structure
Properties stored in S3 with structure: `{accountName}/{language}/{propertyCode}`
- Templates and settings retrieved via `properties-service-{stage}-getSetting`
- Room/rate data cached from external PMS via integrations service

### JWT Authentication Schema
```javascript
{
  "permissions": [{ "ACCOUNTNAME": "own|send|confirm|..." }],
  "accountName": "ACCOUNT",
  "operatorId": "<EMAIL>",
  "exp": timestamp
}
```

### Service Response Format
All services return standardized responses:
```javascript
{ success: boolean, response: any, error?: { message: string, type: string } }
```

## Build & Development Workflows

### Installation & Setup
```bash
yarn install                    # Root dependencies
npm run install-all            # All service dependencies
npm run test                   # Run all service tests
```

### Gulp-based Deployment
Deployment controlled by `Gulpfile.js` with service dependencies defined in `deploymentTargets`. Use:
```bash
gulp units-deploy --stage your-name --domain example.com --region eu-central-1
```

### Commands System
Manual operations via `./commands/run`:
```bash
./commands/run --help                              # List available commands
./commands/run tables/list --from table-name      # Export data
```

## Testing Patterns

### Lambda Invocation Mocking
Services use `test/mock/lambdaInvocation.js` pattern:
```javascript
const lambdaMock = require('./mock/lambdaInvocation');
beforeEach(() => {
  lambdaMock.mock();
  lambdaMock.register('function-name', mockResponse);
});
```

### GraphQL Integration Tests
High-level tests in `/test` directory require running environment and use `utils/graphql.js` for authenticated requests.

## Integration Patterns

### Webhotelier PMS Integration
Primary external system accessed via `integrations` service:
- Availability checks return availability + services data
- Room/rate/service codes must match PMS structure
- Auto-mode properties require `serviceCodes` and `serviceDefs` in offers

### Custom Attributes
Extensible validation via `properties-service` schemas and storage in `attribute-store` service.

## Key File Patterns

- `serverless.yml`: Service configuration with environment variables pointing to other service functions
- `handlers/`: Lambda function implementations
- `lib/`: Business logic and service communication
- `errors/`: Custom error types extending base CustomError
- `middlewares/`: Request/response processing and logging
- `test/`: Service-specific unit tests with mocking

## Common Gotchas

1. **Service naming**: Functions named as `{service}-{stage}-{function}`
2. **Environment variables**: All services expect `STAGE` and `REGION` environment variables  
3. **Logging**: Use `@quotelier/logger` with structured logging: `logger.log(context, message, data)`
4. **State validation**: Always check current request state before applying actions
5. **Backwards compatibility**: When updating offer structures, maintain both old and new formats

## Debugging Commands

```bash
# Check service logs
aws logs tail /aws/lambda/service-name-stage-function --follow

# Export request data
./commands/run tables/list --from new-request-quote-sse-requests --fields contact

# Test GraphQL locally
cd examples/graphQLExpress && npm start
```