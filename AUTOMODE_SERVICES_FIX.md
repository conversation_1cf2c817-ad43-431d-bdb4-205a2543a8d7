# Auto Mode Services Fix

## Problem Description

When creating a new request in auto mode, the system was fetching required services from the integrations service but they were not appearing in the rendered proposal. The issue was in the service population and data structure.

## Root Cause Analysis

1. **Service Fetching**: The `getAvailabilityOffers` function in `createNewRequest.js` was correctly fetching services from the integrations service using `getServicesAvailability`.

2. **Service Filtering**: The function was correctly filtering for required services only (`required: 1`).

3. **Data Structure Issue**: The system was only setting `serviceCodes` but not `serviceDefs`. According to the schema in `services/newRequest/lib/schemas/offer.js`, `serviceCodes` is deprecated and `serviceDefs` is the preferred format.

4. **Service Population**: The `populateRequestService` function in `populateRequest.js` was working correctly, but it depends on having `serviceCodes` populated.

## Fix Implementation

### 1. Fixed Service API Call Parameter

Changed the parameter name from `rateId` to `rate` in the `getServicesAvailability` call:

```javascript
// Before
const result = await invokeService('integrations', 'getServicesAvailability', {
  // ... other params
  rateId: availability.rateId,
  // ...
});

// After  
const result = await invokeService('integrations', 'getServicesAvailability', {
  // ... other params
  rate: availability.rateId,
  // ...
});
```

### 2. Enhanced Service Data Structure

Updated the offer creation to include both `serviceCodes` (for backward compatibility) and `serviceDefs` (preferred format):

```javascript
// Before
const offer = {
  // ... other properties
  serviceCodes: requiredServices[index] || [],
  // ...
};

// After
const offer = {
  // ... other properties
  serviceCodes: serviceIds, // Keep for backward compatibility
  // ...
};

// Add serviceDefs if there are services (this is the preferred format)
if (serviceIds.length > 0) {
  offer.serviceDefs = {
    defs: serviceIds.map(serviceId => ({
      code: serviceId,
      rate: 0 // We don't have rate information from the services API, set to 0
    })),
    totalRate: 0 // We don't have total rate information, set to 0
  };
}
```

### 3. Improved Error Handling and Logging

Added better error handling for service API failures and enhanced logging:

```javascript
try {
  const result = await invokeService('integrations', 'getServicesAvailability', {
    // ... params
  });
  
  const services = result?.response;
  if (!Array.isArray(services)) {
    logger.log('getAvailabilityOffers', `No services returned for availability ${availability.rateId}`, { availability });
    return [];
  }

  const requiredServiceIds = services
    .filter(service => service?.required === 1)
    .filter(service => service.serviceId != null)
    .map(service => service.serviceId);

  logger.log('getAvailabilityOffers', `Found ${requiredServiceIds.length} required services for availability ${availability.rateId}`, { 
    availability: availability.rateId, 
    services: requiredServiceIds 
  });

  return requiredServiceIds;

} catch (error) {
  logger.error('getAvailabilityOffers', `Failed to get services for availability ${availability.rateId}`, {
    error: error.message,
    availability
  });
  return []; // Return empty array on error to not break the entire flow
}
```

## How Services Work in the System

### 1. Service Fetching (Auto Mode)
- When a property has `automode.enabled = true`, the `createNewRequest` handler calls `getAvailabilityOffers`
- This function fetches availability from the integrations service
- For each availability result, it calls `getServicesAvailability` to get associated services
- Only services with `required: 1` are included in the offer

### 2. Service Storage
- Services are stored in two formats:
  - `serviceCodes`: Array of service IDs (deprecated but kept for compatibility)
  - `serviceDefs`: Object with `defs` array and `totalRate` (preferred format)

### 3. Service Population (Rendering)
- When rendering a proposal, the `populateRequest` function is called
- The `populateRequestService` function checks for `serviceCodes` in each offer
- If `serviceCodes` exist, it calls the services API to get full service details
- The populated services are added to `offer.services` array

### 4. Service Rendering
- The rendering templates can access services through `offer.services`
- Each service object contains: `id`, `name`, `description`, `price`, etc.

## Manual Testing

To test the fix manually:

### 1. Create a Property with Auto Mode
```json
{
  "automode": {
    "enabled": true,
    "template": "hybrid",
    "operator": "operator-id",
    "delayMinutes": 15,
    "prefix": "Premium",
    "bookingCode": "AUTO123"
  }
}
```

### 2. Ensure Services are Available
Make sure the integrations service returns services with `required: 1` for the availability results.

### 3. Create a New Request
Use the `createNewRequest` handler with a request that matches the auto mode property.

### 4. Verify Service Population
Check that:
- The created request has `serviceCodes` populated in offers
- The created request has `serviceDefs` populated in offers
- When rendering the request, services are populated in `offer.services`

### 5. Check Logs
Look for log messages like:
- `Found X required services for availability RATE_ID`
- `Created offer with X services`

## Files Modified

1. `services/newRequest/handlers/createNewRequest.js`
   - Fixed service API parameter name
   - Enhanced service data structure
   - Improved error handling and logging

## Backward Compatibility

The fix maintains backward compatibility by:
- Keeping `serviceCodes` populated for existing code that depends on it
- Adding `serviceDefs` as the preferred format
- Ensuring the `populateRequestService` function continues to work with `serviceCodes`

## Future Improvements

1. **Service Rates**: Currently, service rates are set to 0 in `serviceDefs`. Future enhancement could fetch actual service rates from the services API.

2. **Service Validation**: Add validation to ensure service IDs exist before including them in offers.

3. **Caching**: Consider caching service responses to improve performance when multiple offers use the same services.

4. **Testing**: Create integration tests that properly mock the AWS services to test the complete flow.