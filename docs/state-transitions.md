---
markmap:
  colorFreezeLevel: 2
  maxWidth: 240
  initialExpandLevel: 3
---

# Request State Transitions

## State Overview

### Primary States
- **NEW**: Initial request creation
- **DRAFT**: Request being prepared
- **PENDING**: Queued for sending
- **SENT**: Sent to customer
- **OPENED**: Customer viewed request
- **ACCEPTED**: Customer accepted offer
- **CONFIRMED**: Payment completed
- **REJECTED**: Customer declined
- **EXPIRED**: Time limit exceeded
- **CANCELLED**: Request cancelled
- **ARCHIVED**: Moved to archive
- **UNAVAILABLE**: No availability
- **WAITING**: In waitlist queue

## State Transition Flows

### NEW State Transitions
- **NEW** → **DRAFT**
  - **Action**: OWN
  - **Trigger**: Operator takes ownership
  - **Validation**: Operator permissions required
  - **Business Logic**: Assigns operatorId to request

- **NEW** → **EXPIRED**
  - **Action**: EXPIRE
  - **Trigger**: Automatic time-based expiration
  - **Validation**: Expiration time reached
  - **Business Logic**: Scheduled job triggers transition

- **NEW** → **ARCHIVED**
  - **Action**: ARCHIVE
  - **Trigger**: Manual operator action
  - **Validation**: Archive permissions required
  - **Business Logic**: Request moved to archive

- **NEW** → **WAITING**
  - **Action**: WAITLIST
  - **Trigger**: No immediate availability
  - **Validation**: Property supports waitlist
  - **Business Logic**: Added to waitlist queue

### DRAFT State Transitions
- **DRAFT** → **PENDING**
  - **Action**: SEND
  - **Trigger**: Operator sends request
  - **Validation**: Request completeness check
  - **Business Logic**: Queued for email delivery

- **DRAFT** → **EXPIRED**
  - **Action**: EXPIRE
  - **Trigger**: Automatic expiration
  - **Validation**: Time limit reached
  - **Business Logic**: Draft timeout handling

- **DRAFT** → **ARCHIVED**
  - **Action**: ARCHIVE
  - **Trigger**: Manual operator action
  - **Validation**: Archive permissions
  - **Business Logic**: Draft discarded

- **DRAFT** → **WAITING**
  - **Action**: WAITLIST
  - **Trigger**: Move to waitlist
  - **Validation**: Waitlist eligibility
  - **Business Logic**: Queue for future availability

### PENDING State Transitions
- **PENDING** → **SENT**
  - **Action**: Automatic
  - **Trigger**: Email successfully sent
  - **Validation**: Email delivery confirmation
  - **Business Logic**: Customer notification sent

- **PENDING** → **EXPIRED**
  - **Action**: EXPIRE
  - **Trigger**: Send timeout
  - **Validation**: Maximum send attempts reached
  - **Business Logic**: Failed delivery handling

### SENT State Transitions
- **SENT** → **OPENED**
  - **Action**: Automatic
  - **Trigger**: Customer clicks email link
  - **Validation**: Valid request token
  - **Business Logic**: Customer engagement tracked

- **SENT** → **EXPIRED**
  - **Action**: EXPIRE
  - **Trigger**: Response timeout
  - **Validation**: Expiration time reached
  - **Business Logic**: No customer response

- **SENT** → **DRAFT**
  - **Action**: REVERT
  - **Trigger**: Operator reverts request
  - **Validation**: Revert permissions
  - **Business Logic**: Return to editing state

- **SENT** → **ARCHIVED**
  - **Action**: ARCHIVE
  - **Trigger**: Manual archival
  - **Validation**: Archive permissions
  - **Business Logic**: Request archived

- **SENT** → **ACCEPTED**
  - **Action**: ACCEPT
  - **Trigger**: Customer accepts offer
  - **Validation**: Offer still valid
  - **Business Logic**: Offer acceptance processing

- **SENT** → **UNAVAILABLE**
  - **Action**: Automatic
  - **Trigger**: Availability check fails
  - **Validation**: Real-time availability
  - **Business Logic**: No rooms available

### OPENED State Transitions
- **OPENED** → **ACCEPTED**
  - **Action**: ACCEPT
  - **Trigger**: Customer accepts offer
  - **Validation**: Offer validation
  - **Business Logic**: Reservation creation initiated

- **OPENED** → **REJECTED**
  - **Action**: Customer decline
  - **Trigger**: Customer rejects offer
  - **Validation**: Valid rejection
  - **Business Logic**: Rejection processing

- **OPENED** → **EXPIRED**
  - **Action**: EXPIRE
  - **Trigger**: Response timeout
  - **Validation**: Time limit reached
  - **Business Logic**: Customer inactivity

- **OPENED** → **DRAFT**
  - **Action**: REVERT
  - **Trigger**: Operator modification needed
  - **Validation**: Revert permissions
  - **Business Logic**: Return for editing

- **OPENED** → **ARCHIVED**
  - **Action**: ARCHIVE
  - **Trigger**: Manual archival
  - **Validation**: Archive permissions
  - **Business Logic**: Request archived

- **OPENED** → **UNAVAILABLE**
  - **Action**: Automatic
  - **Trigger**: Availability lost
  - **Validation**: Real-time check
  - **Business Logic**: Rooms no longer available

### ACCEPTED State Transitions
- **ACCEPTED** → **CONFIRMED**
  - **Action**: CONFIRM
  - **Trigger**: Payment completed
  - **Validation**: Payment verification
  - **Business Logic**: Reservation confirmed

- **ACCEPTED** → **EXPIRED**
  - **Action**: EXPIRE
  - **Trigger**: Payment timeout
  - **Validation**: Payment deadline reached
  - **Business Logic**: Payment window closed

- **ACCEPTED** → **DRAFT**
  - **Action**: REVERT
  - **Trigger**: Operator modification
  - **Validation**: Revert permissions
  - **Business Logic**: Return for changes

- **ACCEPTED** → **ARCHIVED**
  - **Action**: ARCHIVE
  - **Trigger**: Manual archival
  - **Validation**: Archive permissions
  - **Business Logic**: Request archived

- **ACCEPTED** → **ACCEPTED**
  - **Action**: ACCEPT (different offer)
  - **Trigger**: Customer changes selection
  - **Validation**: New offer validation
  - **Business Logic**: Offer switching

### CONFIRMED State Transitions
- **CONFIRMED** → **EXPIRED**
  - **Action**: EXPIRE
  - **Trigger**: Post-confirmation timeout
  - **Validation**: Final expiration
  - **Business Logic**: Cleanup processing

- **CONFIRMED** → **CANCELLED**
  - **Action**: CANCEL
  - **Trigger**: Cancellation request
  - **Validation**: Cancellation policy
  - **Business Logic**: Reservation cancellation

- **CONFIRMED** → **DRAFT**
  - **Action**: REVERT
  - **Trigger**: Modification needed
  - **Validation**: Modification permissions
  - **Business Logic**: Reservation modification

- **CONFIRMED** → **ARCHIVED**
  - **Action**: ARCHIVE
  - **Trigger**: Final archival
  - **Validation**: Archive permissions
  - **Business Logic**: Completed request archival

### UNAVAILABLE State Transitions
- **UNAVAILABLE** → **ACCEPTED**
  - **Action**: ACCEPT_UNAVAILABLE
  - **Trigger**: Customer accepts waitlist
  - **Validation**: Waitlist acceptance
  - **Business Logic**: Waitlist reservation

- **UNAVAILABLE** → **CANCELLED**
  - **Action**: CANCEL_UNAVAILABLE
  - **Trigger**: Customer cancels
  - **Validation**: Cancellation allowed
  - **Business Logic**: Unavailable request cancelled

- **UNAVAILABLE** → **WAITING**
  - **Action**: WAITLIST
  - **Trigger**: Move to waitlist
  - **Validation**: Waitlist eligibility
  - **Business Logic**: Queue for availability

- **UNAVAILABLE** → **DRAFT**
  - **Action**: REVERT
  - **Trigger**: Operator modification
  - **Validation**: Revert permissions
  - **Business Logic**: Return for editing

- **UNAVAILABLE** → **EXPIRED**
  - **Action**: EXPIRE
  - **Trigger**: Unavailable timeout
  - **Validation**: Time limit reached
  - **Business Logic**: Unavailable cleanup

- **UNAVAILABLE** → **ARCHIVED**
  - **Action**: ARCHIVE
  - **Trigger**: Manual archival
  - **Validation**: Archive permissions
  - **Business Logic**: Unavailable archived

### WAITING State Transitions
- **WAITING** → **EXPIRED**
  - **Action**: EXPIRE
  - **Trigger**: Waitlist timeout
  - **Validation**: Maximum wait time
  - **Business Logic**: Waitlist cleanup

- **WAITING** → **ARCHIVED**
  - **Action**: ARCHIVE
  - **Trigger**: Manual archival
  - **Validation**: Archive permissions
  - **Business Logic**: Waitlist archived

- **WAITING** → **PENDING**
  - **Action**: Automatic
  - **Trigger**: Availability found
  - **Validation**: Matching availability
  - **Business Logic**: Waitlist activation

### Terminal States

#### REJECTED State Transitions
- **REJECTED** → **DRAFT**
  - **Action**: REVERT
  - **Trigger**: Operator retry
  - **Validation**: Retry permissions
  - **Business Logic**: New attempt preparation

- **REJECTED** → **ARCHIVED**
  - **Action**: ARCHIVE
  - **Trigger**: Final archival
  - **Validation**: Archive permissions
  - **Business Logic**: Rejection archived

#### EXPIRED State Transitions
- **EXPIRED** → **DRAFT**
  - **Action**: REVERT
  - **Trigger**: Operator revival
  - **Validation**: Revival permissions
  - **Business Logic**: Request reactivation

- **EXPIRED** → **ARCHIVED**
  - **Action**: ARCHIVE
  - **Trigger**: Final cleanup
  - **Validation**: Archive permissions
  - **Business Logic**: Expired archived

#### CANCELLED State Transitions
- **CANCELLED** → **DRAFT**
  - **Action**: REVERT
  - **Trigger**: Operator reactivation
  - **Validation**: Reactivation permissions
  - **Business Logic**: Cancellation reversal

- **CANCELLED** → **ARCHIVED**
  - **Action**: ARCHIVE
  - **Trigger**: Final archival
  - **Validation**: Archive permissions
  - **Business Logic**: Cancelled archived

#### ARCHIVED State
- **ARCHIVED** → No transitions
  - **Final State**: Terminal state
  - **Business Logic**: Permanent storage

## Action Definitions

### Manual Actions
- **OWN**: Operator takes ownership
- **SEND**: Send request to customer
- **FOLLOWUP**: Send reminder email
- **ACCEPT**: Customer accepts offer
- **CONFIRM**: Complete payment
- **REVERT**: Return to previous state
- **CANCEL**: Cancel request
- **ARCHIVE**: Move to archive
- **WAITLIST**: Add to waitlist
- **EXTEND**: Extend expiration time

### Automatic Actions
- **EXPIRE**: Time-based expiration
- **Email Delivery**: Automatic PENDING → SENT
- **Customer View**: Automatic SENT → OPENED
- **Payment Success**: Automatic ACCEPTED → CONFIRMED
- **Availability Check**: Automatic availability validation

## Business Rules

### Validation Rules
- State transitions must follow defined paths
- Actions require appropriate permissions
- Time-based transitions are automated
- Customer actions validated against current state

### Scheduling Rules
- Expiration times calculated based on business rules
- Follow-up reminders scheduled automatically
- Waitlist processing triggered by availability events
- Payment timeouts enforced strictly

### Permission Rules
- Operators need specific permissions for actions
- Customer actions limited to valid states
- System actions bypass permission checks
- Archive actions require elevated permissions
