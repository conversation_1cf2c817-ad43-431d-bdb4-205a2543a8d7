# Quotelier Serverless Project - Comprehensive Onboarding Guide

## Table of Contents
1. [Project Overview](#project-overview)
2. [Entity Relationships](#entity-relationships)
3. [Request State Management](#request-state-management)
4. [Third-Party Integrations](#third-party-integrations)
5. [Property Information Management](#property-information-management)
6. [Reservation System](#reservation-system)
7. [Visual Documentation](#visual-documentation)

## 1. Project Overview

### Purpose
Quotelier is a serverless hospitality platform built on AWS Lambda that facilitates property booking requests, reservations, and customer communications. The system acts as an intermediary between customers seeking accommodations and property operators, providing a comprehensive workflow for managing booking inquiries from initial request to confirmed reservation.

### Main Functionality
- **Request Management**: Handle customer booking inquiries with sophisticated state management
- **Property Integration**: Connect with external property management systems (primarily Webhotelier)
- **Reservation Processing**: Create, manage, and track reservations across multiple properties
- **Communication**: Automated email notifications and customer communications
- **Payment Processing**: Integration with payment gateways for secure transactions
- **Operator Management**: Tools for property operators to manage requests and reservations

### Architecture
The system follows a microservices architecture deployed on AWS using the Serverless Framework:

- **GraphQL Service**: Main API endpoint for client applications
- **Properties Service**: Property data management and caching
- **Integrations Service**: External API connections and adapters
- **New Request Service**: Request lifecycle management
- **Notifications Service**: Email and communication handling
- **Upsales Service**: Upgrade and upselling opportunities
- **Workflows Service**: Step Functions for complex business processes

### Technology Stack
- **Runtime**: Node.js 14.x/16.x
- **Framework**: Serverless Framework
- **Database**: DynamoDB for primary storage, S3 for file storage
- **Search**: Elasticsearch for indexing and search capabilities
- **Communication**: SNS/SQS for event-driven architecture
- **Authentication**: JWT tokens for API access
- **Monitoring**: Sentry for error tracking, CloudWatch for logging

## 2. Entity Relationships

### Core Entities

#### Account
- **Purpose**: Represents a property management company or hotel group
- **Key Fields**: `accountName` (primary key), `propertyCode`, `type`
- **Relationships**: One-to-many with Properties

#### Property
- **Purpose**: Individual hotel or accommodation property
- **Key Fields**: `code` (ID), `name`, `description`, `currency`, `location`, `contact`
- **Storage**: S3 bucket with structure `{accountName}/{language}/{propertyCode}`
- **Relationships**: 
  - Belongs to Account
  - One-to-many with Rooms
  - One-to-many with Rates

#### Room
- **Purpose**: Accommodation units within a property
- **Key Fields**: `code` (ID), `name`, `description`, `capacity`, `amenities`
- **Relationships**:
  - Belongs to Property
  - One-to-many with Rates

#### Rate
- **Purpose**: Pricing and availability information for rooms
- **Key Fields**: `id`, `name`, `currency`, `constraints`, `policies`
- **Relationships**:
  - Belongs to Room
  - Referenced by Offers

#### Request
- **Purpose**: Customer booking inquiry
- **Key Fields**: `id`, `state`, `operatorId`, `contact`, `request` (booking details)
- **Storage**: DynamoDB table `{service}-{stage}-sse-requests`
- **Relationships**:
  - Contains multiple Offers
  - Belongs to Account/Property
  - Associated with Operator

#### Offer
- **Purpose**: Specific accommodation option within a request
- **Key Fields**: `rateId`, `accommodationCodes`, `serviceCodes`, `price`, `accepted`
- **Relationships**:
  - Belongs to Request
  - References Rate and Room
  - May have associated Reservation

#### Reservation
- **Purpose**: Confirmed booking
- **Key Fields**: `reservationId`, `status`, `propertyCode`, `accommodationCode`, `contact`
- **Relationships**:
  - Created from accepted Offer
  - Belongs to Property
  - Associated with external PMS reservation

#### Operator
- **Purpose**: Property staff managing requests
- **Key Fields**: `id`, `fullName`, `email`, `permissions`
- **Storage**: S3 bucket for operator definitions
- **Relationships**: One-to-many with Requests

### Database Schema

#### DynamoDB Tables
- **SSE Requests Table**: Primary request storage with streaming enabled
- **Accounts Properties Table**: Account-property relationships
- **Operator Tag Table**: Operator categorization
- **Attributes Table**: Custom property attributes
- **Wipelist/Requestlist Tables**: GDPR compliance data

#### S3 Buckets
- **Properties Bucket**: Property, room, and rate data
- **Templates Bucket**: Email and document templates
- **Operators Bucket**: Operator definitions and settings
- **Cache Bucket**: Integration response caching

## 3. Request State Management

### Request States
The system manages requests through a comprehensive state machine with the following states:

#### Primary States
- **NEW**: Initial state when request is created
- **DRAFT**: Request being prepared by operator
- **PENDING**: Request queued for sending
- **SENT**: Request sent to customer
- **OPENED**: Customer has viewed the request
- **ACCEPTED**: Customer has accepted an offer
- **CONFIRMED**: Payment completed, reservation confirmed
- **REJECTED**: Customer declined the request
- **EXPIRED**: Request exceeded time limits
- **CANCELLED**: Request cancelled by operator or customer
- **ARCHIVED**: Request moved to archive
- **UNAVAILABLE**: No availability for requested dates
- **WAITING**: Request in waitlist queue

### State Transitions

#### Allowed Transitions
Each state has specific allowed transitions defined in the system:

```javascript
// Example transitions from SENT state
STATE_MOVES[REQUEST_STATE.SENT] = [
  REQUEST_STATE.OPENED,      // Customer views request
  REQUEST_STATE.EXPIRED,     // Time limit exceeded
  REQUEST_STATE.DRAFT,       // Revert to editing
  REQUEST_STATE.ARCHIVED,    // Archive request
  REQUEST_STATE.ACCEPTED,    // Customer accepts
  REQUEST_STATE.UNAVAILABLE  // No availability
];
```

#### Actions and Triggers
- **OWN**: Operator takes ownership of new request
- **SEND**: Send request to customer
- **FOLLOWUP**: Send reminder to customer
- **ACCEPT**: Customer accepts offer
- **CONFIRM**: Complete payment and confirm reservation
- **EXPIRE**: Automatic expiration based on time limits
- **REVERT**: Return to previous state
- **ARCHIVE**: Move to archive
- **WAITLIST**: Add to waiting list

### Business Logic Rules

#### Validation Rules
- State transitions must follow defined paths
- Certain actions require operator permissions
- Time-based transitions (expiration) are automated
- Payment confirmation triggers reservation creation

#### Scheduling
- **Expiration Scheduling**: Automatic state changes based on time limits
- **Follow-up Reminders**: Scheduled customer communications
- **Waitlist Processing**: Automatic matching when availability opens

## 4. Third-Party Integrations

### External Services Overview
The system integrates with multiple external services through a standardized adapter pattern:

#### Property Management Systems (PMS)
- **Webhotelier**: Primary PMS integration
  - **Endpoint**: `https://rest.reserve-online.net`
  - **Authentication**: Basic Auth (username/password)
  - **Rate Limiting**: Managed through caching layer
  - **Functions**: Property data, availability, reservations, rates

#### Payment Processors
- **Unicre/Reduniq**: Payment gateway integration
  - **Endpoint**: `https://pagamentos.reduniq.pt/api-payments/v1.0/`
  - **Authentication**: API Key + Merchant ID
  - **Functions**: Payment URL generation, transaction processing

#### Email Services
- **Mandrill**: Transactional email delivery
  - **Authentication**: API Token
  - **Features**: Template rendering, tracking, metadata
  - **Rate Limiting**: Built-in Mandrill limits

### Integration Architecture

#### Adapter Pattern
All integrations follow a standardized interface:

```javascript
interface IntegrationAdaptorInterface {
  getProperty(input): Promise<QProperty>
  getAvailability(input): Promise<AvailabilityResponse>
  createReservation(input): Promise<ReservationResponse>
  cancelReservation(input): Promise<CancelResponse>
  getRates(input): Promise<QRate[]>
}
```

#### Caching Strategy
- **Cache Duration**: 7 days default
- **Cache Keys**: Hashed input parameters
- **Fallback**: Expired cache used if API fails
- **Storage**: S3 bucket for cached responses

#### Error Handling
- **Custom Error Types**: NotFoundError, ForbiddenError, IntegrationError
- **Retry Logic**: Exponential backoff for transient failures
- **Circuit Breaker**: Prevent cascade failures
- **Monitoring**: Sentry integration for error tracking

#### Authentication Methods
- **Webhotelier**: Basic Authentication with property-specific credentials
- **Unicre**: API Key authentication with merchant validation
- **Mandrill**: Token-based authentication

### API Endpoints and Usage

#### Webhotelier Integration
- **Property Data**: `GET /property/{propertyCode}`
- **Availability**: `GET /availability/{propertyCode}`
- **Reservations**: `POST /reservation/{propertyCode}`
- **Rates**: `GET /rate/{propertyCode}/{roomCode}`

#### Required Headers
- `Accept: application/json`
- `Accept-Language: {language}`
- `Accept-Encoding: gzip` (Required for performance)

## 5. Property Information Management

### Data Retrieval Process

#### External Source Integration
Property data is retrieved from external PMS systems through the integrations service:

1. **Initial Request**: Client requests property information
2. **Account Lookup**: System identifies the correct PMS adapter
3. **API Call**: Adapter makes authenticated request to external system
4. **Data Transformation**: Raw data mapped to Quotelier schema
5. **Validation**: Data validated against business rules
6. **Storage**: Processed data stored in S3 for caching

#### Data Transformation Pipeline
```javascript
// Example transformation flow
rawPropertyData → mapProperty() → validateProperty() → storeProperty()
```

### Storage Strategy

#### S3 Structure
Properties are stored in S3 with hierarchical organization:
```
properties-bucket/
├── {accountName}/
│   ├── {language}/
│   │   ├── {propertyCode}          # Property details
│   │   ├── {propertyCode}/rooms/   # Room information
│   │   └── {propertyCode}/rates/   # Rate information
```

#### Data Format
- **Format**: JSON
- **Compression**: Gzip for large datasets
- **Versioning**: S3 versioning enabled for data history
- **Access**: Pre-signed URLs for secure access

### Caching Mechanisms

#### Multi-Level Caching
1. **S3 Cache**: Long-term storage of processed data
2. **Integration Cache**: API response caching (7 days)
3. **Lambda Memory**: In-memory caching for frequently accessed data

#### Cache Invalidation
- **Time-based**: Automatic expiration after configured duration
- **Event-based**: Manual invalidation on data updates
- **Fallback Strategy**: Serve stale data if source unavailable

### Data Synchronization

#### Synchronization Triggers
- **Scheduled Updates**: Daily property data refresh
- **Event-driven**: Updates triggered by PMS webhooks
- **Manual Refresh**: Operator-initiated data updates

#### Conflict Resolution
- **Last Write Wins**: Simple conflict resolution strategy
- **Version Control**: S3 versioning for data recovery
- **Validation**: Schema validation prevents corrupt data

## 6. Reservation System

### Reservation Creation Workflow

#### Standard Booking Flow
1. **Availability Check**: Verify room availability for requested dates
2. **Rate Calculation**: Calculate pricing including taxes and services
3. **Offer Creation**: Generate booking offers for customer
4. **Customer Selection**: Customer chooses preferred offer
5. **Reservation Creation**: Create reservation in external PMS
6. **Payment Processing**: Generate payment URL and process payment
7. **Confirmation**: Send confirmation to customer and operator

#### Code Example - Reservation Creation
```javascript
function createReservation(request, offer) {
  const payload = createReservationPayload(request, offer);

  return invokeLambda(CREATE_RESERVATION_FUNCTION_NAME, payload)
    .then(response => {
      if (response.success) {
        return updateRequestAfterReservation(request.id, offer.id, response.response);
      } else {
        throw new IntegrationError(response.error.message);
      }
    });
}
```

### Availability Checking

#### Real-time Availability
- **Source**: External PMS systems (Webhotelier)
- **Caching**: Short-term caching (minutes) for performance
- **Validation**: Cross-check multiple room types and rates

#### Conflict Resolution
- **Optimistic Locking**: Assume availability until reservation attempt
- **Retry Logic**: Automatic retry with alternative options
- **Waitlist**: Queue requests when no immediate availability

### Booking Validation

#### Pre-booking Validation
- **Date Validation**: Check-in/check-out date logic
- **Capacity Validation**: Guest count vs. room capacity
- **Rate Validation**: Pricing and availability confirmation
- **Policy Validation**: Cancellation and payment policies

#### Business Rules
- **Minimum Stay**: Property-specific minimum night requirements
- **Maximum Occupancy**: Room capacity limits
- **Age Restrictions**: Child and infant policies
- **Seasonal Restrictions**: Blackout dates and seasonal rules

### Payment Processing

#### Payment Flow
1. **Payment URL Generation**: Create secure payment link
2. **Customer Redirect**: Direct customer to payment gateway
3. **Payment Completion**: Process payment through gateway
4. **Webhook Handling**: Receive payment confirmation
5. **Reservation Confirmation**: Finalize reservation in PMS

#### Payment Gateways
- **Unicre/Reduniq**: Primary payment processor
- **Security**: PCI-compliant payment handling
- **Currencies**: Multi-currency support based on property

### Cancellation and Modification

#### Cancellation Process
1. **Cancellation Request**: Customer or operator initiates cancellation
2. **Policy Check**: Validate against cancellation policies
3. **PMS Update**: Cancel reservation in external system
4. **Refund Processing**: Handle refunds according to policy
5. **Notification**: Inform all parties of cancellation

#### Modification Workflow
- **Availability Check**: Verify new dates/requirements
- **Price Adjustment**: Calculate price differences
- **PMS Update**: Modify reservation in external system
- **Confirmation**: Send updated confirmation

#### Waitlist Management
When cancellations occur, the system automatically:
1. **Identify Candidates**: Find waitlisted requests matching criteria
2. **Notify Operators**: Alert property staff of new availability
3. **Process Requests**: Convert waitlist requests to active offers

## 7. Visual Documentation

### Entity Relationship Diagram
See `docs/entity-relationships.mm` - Interactive markmap showing:
- All data entities and their properties
- Primary and foreign key relationships
- One-to-many and many-to-many connections
- Database table structures

### State Transition Diagram
See `docs/state-transitions.mm` - Interactive markmap showing:
- All possible request states
- Allowed state transitions
- Action triggers for each transition
- Business rules and validation points

### Getting Started

#### Prerequisites
- Node.js 14.x or higher
- AWS CLI configured with Quotelier profile
- Serverless Framework installed globally
- Access to development AWS account

#### Development Setup
1. Clone repository and install dependencies:
   ```bash
   yarn install
   npm run install-all
   ```

2. Configure AWS credentials in `~/.aws/credentials`:
   ```
   [quotelier]
   aws_access_key_id=your_key
   aws_secret_access_key=your_secret
   ```

3. Deploy services to development stage:
   ```bash
   STAGE=your-name REGION=eu-central-1 npm run deploy
   ```

#### Testing
- Unit tests: `npm run test` in each service directory
- Integration tests: `cd test && npm run test`
- Local development: Use examples/graphQLExpress for local GraphQL server

#### Key Concepts for New Developers
- **Serverless Architecture**: Each service is independently deployable
- **Event-Driven**: Services communicate via SNS/SQS events
- **State Management**: Requests follow strict state machine patterns
- **Caching Strategy**: Multi-level caching for performance
- **Error Handling**: Comprehensive error tracking and recovery
- **Security**: JWT-based authentication and authorization

