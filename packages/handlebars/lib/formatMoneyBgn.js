const accounting = require('accounting');

/**
 * If currency is EUR, it converts and formats the amount to BGN currency 
 * if currency is BGN, it converts and formats the amount to EURO currency
 * and if currency is neither EUR nor BGN, it returns the amount formatted to the given currency
 * @param {number} amount - The amount to format
 * @param {string} currency - The currency code (EUR, BGN, etc)
 * @returns {string}
 */
module.exports = function formatMoneyBgn(amount, currency) {
  const formatConfig = {
    'EUR': {
      format: '%v %s',
      symbol: 'лв',
      rate: 1.95583
    },
    'BGN': {
      format: '%s %v', 
      symbol: '€',
      rate: 0.511292
    },
    'default': {
      format: '%v %s',
      symbol: currency,
      rate: 1
    }
  };

  const config = formatConfig[currency] || formatConfig.default;

  return accounting.formatMoney(amount * config.rate / 100, {
    symbol: config.symbol,
    format: config.format,
    precision: 2
  });
};
