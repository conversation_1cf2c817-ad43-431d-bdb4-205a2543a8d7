---
markmap:
  colorFreezeLevel: 2
  maxWidth: 240
  initialExpandLevel: 2
---

# Elixir Phoenix Migration

> High‑level phases for migrating the existing Serverless Node.js stack to an Elixir Phoenix based platform.

## Phase 1: Foundation
- Phoenix application generation
- PostgreSQL & Ecto configuration
- Environment setup (dev / test / prod)
- Base folder structure
- Initial CI/CD pipeline

## Phase 2: Core Domain
- Accounts context
- Authentication (sessions / tokens)
- Properties module
- Contracts context

## Phase 3: Data & Integrations
- Data migration from Node services
- External APIs (CRM / Payments)
- Background jobs (Oban)

## Phase 4: Features
- Quotes flow
- Notifications system
- Reporting dashboards

## Phase 5: Hardening
- Performance tuning
- Observability (Telemetry / Metrics)
- Security review
- Load / stress tests

## Phase 6: Launch
- Migration dry run
- Cutover plan
- Rollback strategy
- Post‑launch monitoring

---
### Legend / Notes
- Each phase can become a Linear Project or Milestone.
- Break items into issues; keep phases outcome‑oriented.
- Revisit after each phase for scope adjustment.

### Next Refinements
- Add timelines & owners (RACI)
- Attach risk register
- Define KPIs per phase (e.g., p95 latency target in Hardening)
      