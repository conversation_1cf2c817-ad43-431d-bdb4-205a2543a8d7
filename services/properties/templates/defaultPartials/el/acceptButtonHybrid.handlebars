<div class='ui hidden divider'></div>
<button class='ui fluid button text-uc offer-action-btn' data-action-prop-loading='{{{serialize @root.metadata.i18n.loading}}}'
  data-action-prop-opened='{{{serialize @root.metadata.i18n.opened}}}' data-action-prop-accepted='{{{serialize @root.metadata.i18n.accepted}}}'
  data-action-prop-rejected='{{{serialize @root.metadata.i18n.rejected}}}' data-action-prop-expired='{{{serialize @root.metadata.i18n.expired}}}'
  data-option-id='{{groupId}}' id='offer-{{groupId}}-action-btn'>
</button>

<div class='ui accept positive small modal' data-id='{{ groupId }}'>
  <i class='close icon'></i>
  <div class='header'>
    Αποδέχεστε την προσφορά;
  </div>
  <div class='scrolling content'>
    <div class='description'>
       Θα δεσμέυσουμε το δωμάτιο και θα επικοινώνησουμε άμεσα μαζί σας για όλους τους τρόπους πληρωμής που έχετε στην διάθεση σας.
    </div>
    <div class='ui hidden divider'></div>
    <form class='ui form accept-offer accept-offer-form-redirect clearfix' data-option-id="{{groupId}}">
      <div class='ui fluid huge input'>
        <label class='ui hidden label'>Ονοματεπώνυμο</label>
        <input name='fullname' placeholder='Ονοματεπώνυμο' required min="18" max="99">
      </div>
      <div class='ui hidden divider'></div>
      <div class='ui checkbox'>
        <input name='policy' type='checkbox' required>
        <label>
          Αποδέχομαι την
        </label>
      </div>
      <a href='#' class='accordion-toggle'>πολιτική ακύρωσης και πληρωμής</a>
      <button class='ui submit positive right floated button' type="submit">
         ΑΠΟΔΕΧΟΜΑΙ ΤΗΝ ΠΡΟΣΦΟΡΑ
      </button>
      <div class='ui hidden divider'></div>
      <div class='ui error message'>
        ERROR MESSAGE
      </div>
      <div class="ui accordion">
        <div class="title"></div>
        <div class="content">
          <h2 class="header">Πολιτική ακύρωσης και πληρωμής</h2>
          {{{ offer.rate.policies.payment }}} {{{ offer.rate.policies.cancellation }}}
        </div>
      </div>
    </form>
  </div>
</div>
