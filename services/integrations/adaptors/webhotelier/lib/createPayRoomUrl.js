const url = require('url');
const qs = require('querystring');
/**
 * Creates a new payment-url with the protocol, hostname and query-parameter: "email" of
 * the summaryUrl and overrides pathname (creditcard), query-parameter: "res_id".
 *
 * This new created payment url is a new version of webhotelier-payment-method, which supports
 * multiple reservation-payments.
 *
 * @param {string} summaryUrl
 * @param {string} reservationId
 * @param {string} flowType
 * @returns {string}
 */
function createPayRoomUrl({ summaryUrl, reservationId, flowType }) {
  const { protocol, hostname, query } = url.parse(summaryUrl);
  const { email } = qs.parse(query);

  return url.format({
    protocol,
    hostname,
    pathname: 'payment/new',
    query: {
      res_id: reservationId,
      email,
      combined: flowType === 'selectMany' ? 1 : 0,
    },
  });
}

module.exports = createPayRoomUrl;
