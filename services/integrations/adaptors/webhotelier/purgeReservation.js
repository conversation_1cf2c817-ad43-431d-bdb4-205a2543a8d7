require('./../interface');
const getPassword = require('../../lib/getPassword');
const { makePOST } = require('./lib/client');
const logger = require('@quotelier/logger');

/**
 * @override
 * @throws Error - Webhotelier's NO OK error
 * @implements {IntegrationAdaptorInterface#purgeReservation}
 */
function purgeReservation(input) {
  const accountName = input.accountName;
  const propertyCode = input.propertyCode;

  return getPassword(accountName, propertyCode)
    .then(password => {
      return makePOST(`purge/${input.reservationId}`, {
        username: propertyCode,
        password: password,
      });
    })
    .then(response => response.result === 'OK')
    .catch(error => {
      if (error.response.statusCode === 400) {
        /**
         * Reservation was already cancelled, handle the bad request status-code and
         * respond with OK.
         */
        const data = JSON.parse(error.response.body);
        if (data['error_msg'] && (data['error_msg'].indexOf('deleted') !== -1 || data['error_msg'].indexOf('cancelled') !== -1)) {
          logger.log(
            'purgeReservation',
            'Reservation already cancelled, handle bad status code and respond with "OK"',
            error.response
          );
          return true;
        }
      }
      throw error;
    });
}

module.exports = {
  purgeReservation,
};
