require('./../interface');
const { makePOST } = require('./lib/client');
const moment = require('moment');
const REDUNIQ_DEFAULT_ERROR_PAGE = 'https://quote.quotelier.net/reduniq-error-page/';
const logger = require('@quotelier/logger');

/**
 * @override
 * @implements {IntegrationAdaptorInterface#createPaymentUri}
 */
function createPaymentUri(input) {
  return makePOST('request', {
    merchantId: input.merchantId,
    apiKey: input.apiKey,
    paymentData: transformPaymentUriPayload(input),
  }).then(response => {
    if (response.retStat === "ok" && response.url) {
      return response.url;
    } else {
      return(process.env.REDUNIQ_DEFAULT_ERROR_PAGE || REDUNIQ_DEFAULT_ERROR_PAGE);
    }
  });
}

function transformPaymentUriPayload(input) {
  logger.log('transformPaymentUriPayload', 'Transform payment uri payload', input); 
  return {
    firstName: input.firstName,
    lastName: input.lastName,
    email: input.email,
    phone: input.phone || '000000000',
    descriptive: input.external_id,
    paymentDeadline: moment()
    .add(3, 'd')
    .format('DD-MM-YYYY'),
    amount: input.price,
    language: 'en'
  };
}


module.exports = { createPaymentUri };
