const request = require('request');
const querystring = require('querystring');
const { NotFoundError, ForbiddenError, IntegrationError } = require('../../../errors');
const logger = require('@quotelier/logger');
const REDUNIQ_ENDPOINT = 'https://pagamentos.reduniq.pt/api-payments/v1.0/';


/**
 * @private
 * @param {string} operation
 * @param {object} options
 * @param {string} options.merchantId
 * @param {string} options.apiKey
 * @param {object} [options.paymentData]
 *
 * @returns {Promise<Response>}
 */
function makePOST(operation, { merchantId, apiKey, paymentData }) {
    logger.log('makePOST', 'reduniq perform request', { merchantId, paymentData });
    const endpoint = process.env.REDUNIQ_ENDPOINT || REDUNIQ_ENDPOINT;
    return new Promise((resolve, reject) => {
      request(
        {
          url: `${endpoint}`,
          method: 'POST',
          headers: {
            Accept: 'application/json',
          },
          json: {
            merchantId: merchantId,
            apiKey: apiKey,
            operation: operation,
            paymentData: paymentData,
          },
        },
        responseHandler.bind(this, resolve, reject, operation)
      );
    });
  }

/**
 * @private
 * @param {Function} resolve
 * @param {Function} reject
 * @param {string} operation
 * @param {Error} err
 * @param {object} response
 * @returns {Promise}
 */
function responseHandler(resolve, reject, operation, err, response) {
    logger.log('responseHandler', 'Receive http response', response);
    if (err) {
      logger.error('responseHandler', 'Error while contacting reduniq', err);
      err.message = `${err.message} (Operation:${operation})`;
      reject(err);
    } else if (response.statusCode === 403) {
      logger.error('responseHandler', 'Error while contacting reduniq', response);
      const error = new ForbiddenError(`Access forbidden (Operation:${operation})`);
      error.response = response;
      reject(error);
    } else if (response.statusCode === 404) {
      logger.error('responseHandler', 'Error while contacting reduniq', response);
      const error = new NotFoundError(`Not found (Operation:${operation})`);
      error.response = response;
      reject(error);
    } else {
      try {
        resolve(response.body);
        // const { url = null, retMsg = null } = response.body;
  
        // if (url) {
        //   resolve(response.body);
        // } else if (retMsg) {
        //   const error = new IntegrationError(retMsg);
        //   error.response = response;
        //   reject(error);
        // }
      } catch (e) {
        logger.error('responseHandler', 'Error while contacting reduniq', response);
        const error = new Error(`Unexpected status-code: ${response.statusCode} (Operation:${operation})`);
        error.response = response;
        reject(error);
      }
    }
  }
  
  module.exports = { makePOST };
  