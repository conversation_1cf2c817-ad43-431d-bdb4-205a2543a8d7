require('./../adaptors/interface');

const assert = require('assert');
const { getAccountRecord } = require('../lib');
const { ValidationError } = require('./../errors');
const getAdaptor = require('./../lib/getAdaptor');
const errorHandler = require('./errorHandler');
const logger = require('@quotelier/logger');
const middlewares = require('../middlewares');

/**
 * @typedef {!LambdaResponse.<PurgeReservationResult>} PurgeReservationResponse
 */

/**
 * Get availability from Webhotelier integration
 *
 * @param {PurgeReservationInput} event
 * @param {Object} context
 * @param {function(Error, (PurgeReservationResponse|null))} callback
 * @returns {void}
 */
function purgeReservation(event, context, callback) {
  logger.log('purgeReservation', 'Purge reservation', event);

  assertValidInput(event)
    .then(() => getAccountRecord(event.accountName, event.propertyCode))
    .then(account => {
      if (!account) {
        throw new ValidationError('Account not found');
      }
      return getAdaptor(account.type).purgeReservation({
        accountName: event.accountName,
        propertyCode: event.propertyCode,
        reservationId: event.reservationId,
      });
    })
    .then(response => callback(null, { success: true, response }))
    .catch(error => errorHandler(error, callback));
}

/**
 * @param {PurgeReservationInput} event
 * @private
 * @returns {Promise<null>}
 */
function assertValidInput(event) {
  return Promise.resolve().then(() => {
    try {
      assert(event.accountName, 'accountName is not defined');
      assert(event.propertyCode, 'propertyCode is not defined');
      assert(event.reservationId, 'reservationId is not defined');
    } catch (e) {
      throw new ValidationError(e.message);
    }
    return null;
  });
}

module.exports = { handler: middlewares.init(purgeReservation) };
