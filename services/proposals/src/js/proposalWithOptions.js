/* eslint-disable */
require('../../node_modules/semantic-ui-css/components/api.js');
require('../../node_modules/semantic-ui-css/components/dimmer.js');
require('../../node_modules/semantic-ui-css/components/modal.js');
require('../../node_modules/semantic-ui-css/components/checkbox.js');
require('../../node_modules/semantic-ui-css/components/embed.js');
require('../../node_modules/semantic-ui-css/components/transition.js');
require('../../node_modules/semantic-ui-css/components/accordion.js');
require('../../node_modules/slick-carousel');
require('./priceConverter.js')

const EXCLUDE_URL_PARAM = /[?&]exclude/;

$(document).ready(bootstrapApp);

function bootstrapApp() {
  checkForExcludeParameter();

  getOffers().forEach(offer => changeState(offer.index, 'loading'));

  if (!shouldBeExcluded()) {
    if (!('__quotelier__' in window) || $.isEmptyObject(window.__quotelier__)) {
      throw new Error('__quotelier__ global object is required');
    }

    performAction(null, 'opened', response => applyRemoteStatus(response));
  } else {
    showPreviewWarning();

    getOffers().forEach(offer => changeState(offer.index, 'opened'));

    $('#reject-main-button').removeClass('disabled');
  }

  attachEventsToElements();
  initializeImageGallery();
}

function checkForExcludeParameter() {
  if (
    containsExcludeParameter(document.location.search) ||
    ('__quotelier__' in window && window.__quotelier__preview)
  ) {
    localStorage.setItem('shouldExclude', true);

    history.replaceState(
      {},
      document.title,
      `${document.location.pathname}${document.location.search.replace(EXCLUDE_URL_PARAM, '')}`
    );
  }
}

function showPreviewWarning() {
  const $warningEl = $('.preview-warning');

  $warningEl.removeClass('hidden');
}

function containsExcludeParameter(string) {
  return EXCLUDE_URL_PARAM.test(string);
}

function shouldBeExcluded() {
  return localStorage.shouldExclude;
}

function acceptOffer() {
  var offerId = getOfferId(this);

  try {
    performAction(offerId, 'acceptedOption', function({ response }) {
      if (response) {
        applyRemoteStatus(response);
      } else {
        applyRemoteStatus({ state: 'expired' });
        changeState(offerId, 'accepted');
      }
    });
  } catch (e) {
    return false;
  }

  getOffers().forEach(offer => changeState(offer.index, 'loading'));

  $('.ui.accepted.modal').modal('show');
  $(`.ui.accept.positive.modal[data-id=${offerId}]`).modal('hide');

  return false;
}

function acceptOfferRedirect() {
  var offerId = getOfferId(this);

  try {
    performAction(offerId, 'acceptedOption', function({ response }) {
      if (response) {
        redirectToPaymentUrl(response)
      } else {
        applyRemoteStatus({ state: 'expired' });
        changeState(offerId, 'accepted');
      }
    });
  } catch (e) {
    return false;
  }

  getOffers().forEach(offer => changeState(offer.index, 'loading'));

  $('.ui.accepted.modal').modal('show');
  $(`.ui.accept.positive.modal[data-id=${offerId}]`).modal('hide');

  return false;
}

function rejectRequest() {
  var selectedReasons = [];

  $('#rejection-form')
    .find('input[type=checkbox]')
    .each(function(index, ele) {
      if ($(ele).is(':checked')) {
        selectedReasons.push($(ele).val());
      }
    });

  applyRemoteStatus({
    state: 'rejected',
  });

  performAction(null, 'rejected', null, {
    reasons: selectedReasons.join('.'),
  });

  $('.ui.rejected.small.modal').modal('show');
  $('.ui.negative.reject.modal').modal('hide');

  return false;
}

function getOfferElementBlocks() {
  console.log(`getOfferElementblocks`);

  return $('.offer-block').toArray();
}

function getOffers() {
  console.log('getOffers');

  return getOfferElementBlocks().map(ele => ({
    index: getOfferId(ele),
    element: ele,
  }));
}

function redirectToPaymentUrl(response) {
  console.log('redirectToPaymentUrl');

  // Get the first not nil payment URL from the response.offers list of objects
  var paymentUrl = response.offers.find(offer => offer.paymentUrl).paymentUrl;

  // Open the payment URL in the same window
  window
    .open(paymentUrl, '_self')
    .focus();
}

function changeState(id, state) {
  console.log(`changeState for id ${id} with state ${state}`);

  var btn = $(`#offer-${id}-action-btn`);
  var propJson = $(btn).attr(`data-action-prop-${state}`);
  var currentPropJson = $(btn).attr('data-current-action-prop');

  if (!propJson) {
    throw new Error(`State ${state} on ${id} is not defined.`);
  }
  if (currentPropJson) {
    removeProperties(btn, JSON.parse(currentPropJson));
  }

  applyProperties(btn, JSON.parse(propJson));
  btn.attr('data-current-state', state);
}

function applyProperties(btn, prop) {
  console.log('applyProperties');

  prop.classes.forEach(btn.addClass.bind(btn));
  btn.text(prop.text);
  btn.attr('data-current-action-prop', JSON.stringify(prop));
}

function removeProperties(btn, prop) {
  console.log('removeProperties');

  prop.classes.forEach(btn.removeClass.bind(btn));
  btn.text('');
}

function performAction(suffix, state, cb, data) {
  console.log('performAction');

  cb = cb || function() {};

  if (!__quotelier__ || !__quotelier__[state]) {
    throw new Error(`No action endpoint for ${state}`);
  }

  if (!shouldBeExcluded()) {
    $.ajax({
      url: __quotelier__[state] + (suffix === null ? '' : suffix),
      crossDomain: true,
      data: data || {},
    }).done(function(response) {
      cb(response);
    });
  }
}

function applyRemoteStatus(remoteStatus) {
  console.log(`applyRemoteStatus ${remoteStatus}`);

  const { proposal } = remoteStatus;

  const isLockedByBackend = proposal ? proposal.state.lock : null;

  if (['expired', 'rejected'].indexOf(remoteStatus.state) !== -1) {
    getOffers().forEach(offer => {
      changeState(offer.index, remoteStatus.state);
    });
  } else if (['draft', 'pending', 'new', 'revert'].indexOf(remoteStatus.state) !== -1) {
    $('.page.dimmer')
      .dimmer({ closable: false })
      .dimmer('show');
  } else if (isLockedByBackend === null) {
    // deprecated endpoint without proposal state response
    remoteStatus.options.forEach(option => {
      changeState(
        option.id,
        option.accepted ? 'accepted' : remoteStatus.state === 'opened' ? 'opened' : 'expired'
      );
    });
  } else if (isLockedByBackend) {
    remoteStatus.options.forEach(option => {
      changeState(option.id, option.accepted ? 'accepted' : 'expired');
    });
  } else if (!isLockedByBackend) {
    remoteStatus.options.forEach(option => {
      changeState(option.id, option.accepted ? 'accepted' : 'opened');
    });
  }

  if (remoteStatus.state === 'opened') {
    $('#reject-main-button').removeClass('disabled');
  } else {
    $('#reject-main-button').addClass('disabled');
  }
}

function attachEventsToElements() {
  $('.offer-action-btn').on('click', handleActionBtn);
  $('.offer-reject-btn').on('click', rejectRequest);
  $('.accept-offer-form').on('submit', acceptOffer);
  $('.accept-offer-form-redirect').on('submit', acceptOfferRedirect);

  $('.offer-block').each((index, ele) => {
    var parent = $(ele);
    parent.find('.ui.policy.modal').modal('attach events', parent.find('a.policy.link'), 'show');
  });

  $('.ui.negative.reject.modal').modal('attach events', '.ui.negative.reject.button', 'show');

  $('.ui.embed').embed();

  $('.ui.accordion').accordion({
    onOpen: function() {
      $('.ui.modal').modal('refresh');
    },
  });

  $('.ui.form.accept-offer a.accordion-toggle').on('click', function(event) {
    event.preventDefault();
    var accordions = $(this).siblings('.ui.accordion');
    if (accordions.length > 0) {
      $(accordions[0]).accordion('toggle', 0);
    }
  });
}

function initializeImageGallery() {
  // Initialize gallery
  $('.qt-image-gallery').each(($this, element, index) => {
    var $element = $(element);
    if ($element && $element.slick) {
      $(element).slick({
        autoplay: true,
        dots: true,
        arrows: false,
      });
    }
  });
}

function handleActionBtn(e) {
  var offerId = getOfferId(e.target);
  $(`.ui.accept.positive.modal[data-id=${offerId}]`).modal('show');
}

/**
 *
 * An offer block may have offer-id or option-id.
 *
 * A proposal may consist of offers or oprions, not both.
 *
 * @param {Element} element
 * @return {String}
 */
function getOfferId(element) {
  return $(element).attr('data-option-id');
}

/* eslint-enable */
