const AWS = require('aws-sdk');
const Promise = require('bluebird');

const logger = require('@quotelier/logger');
const { CustomError } = require('./../errors');
const {
  emptySchedulerForRequest,
  scheduleToMarkToFollowUp,
  scheduleToExpire,
  scheduleToNotifyExpiring,
} = require('../lib/scheduler');
const requestService = require('./../lib/request');
const populateRequest = require('./../lib/populateRequest');
const renderingService = require('./../lib/render');
const { invokeLambda } = require('@quotelier/invokeLambda');
const errorHandler = require('./errorHandler');
const sendEmail = require('./../lib/sendEmail');
const middlewares = require('../middlewares');

const s3 = new AWS.S3();
const ddb = new AWS.DynamoDB.DocumentClient({
  region: process.env.REGION,
});

/**
 * Publish a Request to S3 and make it available to the Customer.
 *
 * @param {Object} event
 * @param {String} event.requestId
 * @param {Object} context
 * @param {Function} callback
 * @returns {void}
 */
function publish(event, context, callback) {
  const requestId = event.requestId;
  let request = null;

  requestService
    .getRequestById(requestId)
    .then((response) => (request = response))
    .then(() => canPublish(request))
    .then(() =>
      requestService.updateRequestToState(requestId, requestService.REQUEST_STATE.PENDING)
    )
    .then(() => bookRequest(request))
    .then(() =>
      renderingService
        .renderRequest(requestId)
        .then((data) =>
          storeProposalToS3(requestId, data.content).then(() =>
            updateRequestStateToSent(requestId, getProposalUri(requestId, data))
          )
        )
    )
    .then(() => sendEmail.sendEmailToCustomer(requestId, 'customerNew', 'new-proposal', 'contact'))
    .then(() => requestService.markRequestToFollowUp(requestId, false))
    .then(() => emptySchedulerForRequest(requestId))
    .then(() => scheduleToMarkToFollowUp(Object.assign({}, request, { state: 'sent' })))
    .then(() => scheduleToExpire(request))
    .then(() => scheduleToNotifyExpiring(request))
    .then(() =>
      callback(null, {
        success: true,
        response: null,
      })
    )
    .catch((err) => {
      if (err instanceof CustomError) {
        return cancelReservations(requestId);
      } else {
        throw err;
      }
    })
    .catch((err) => errorHandler(err, callback));
}

/**
 * Rollback function in case something went wrong.
 *
 * Currently it only cancels previously made reservations.
 *
 * @private
 * @param {QPrimitiveRequest} request
 * @returns {Promise}
 */
function cancelReservations(request) {
  logger.log('cancelReservations', 'Cancel reservations for', { requestId: request.id });

  return Promise.map(request.offers, (offer) => {
    if (offer.reservationId) {
      return invokeLambda(process.env.CANCEL_RESERVATION_FUNCTION_NAME, {
        accountName: request.request.accountName,
        propertyCode: request.request.propertyCode,
        reservationId: offer.reservationId,
      });
    }
  });
}

/**
 * @private
 * @param {string} requestId
 * @param {Object} [data]
 * @returns {string}
 */
function getProposalUri(requestId, data) {
  const defaultUri = `http://${process.env.STAGE}.${process.env.DOMAIN}/${requestId}`;
  return (
    (data && data.metadata && data.metadata.proposal ? data.metadata.proposal.uri : false) ||
    defaultUri
  );
}

/**
 * @param {QRequest} request
 * @returns {boolean}
 */
function canPublish(request) {
  if (!requestService.canMoveStateTo(request.state, requestService.REQUEST_STATE.PENDING)) {
    logger.error('canPublish', `Request state can not move from "${request.state}" to "sent"`);
    throw new Error();
  } else if (!request.operatorId) {
    logger.error('canPublish', 'Request has no operator, publishing not possible');
    throw new Error();
  } else if (!request.offers.length) {
    logger.error('canPublish', 'Request has no offers, publishing not possible');
    throw new Error();
  }

  logger.log('canPublish', 'Request can published');
  return true;
}

/**
 * @private
 * @param {QRequest} request
 * @returns {Promise<any>}
 */
async function bookRequest(request) {
  const populatedRequest = await populateRequest.populateAttributes(request);

  return Promise.each(request.offers, (offer, offerIndex) => bookOffer(populatedRequest, offer, offerIndex));
}

/**
 * @private
 * @param {QRequest} request
 * @param {QOffer} offer
 * @param {number} offerIndex
 * @returns {Promise|boolean}
 */
function bookOffer(request, offer, offerIndex) {
  if (!offer.releaseAt) {
    return false;
  }

  const reservationPayload = createReservationPayload(request, offer);

  logger.log('bookOffer', 'Reserve request offer', { offerIndex, offer, reservationPayload });

  return invokeLambda(process.env.CREATE_RESERVATION_FUNCTION_NAME, reservationPayload).then(
    (response) => {
      logger.log('bookOffer', 'Reservation response', { response });
      if (response.success) {
        return updateOffer(request.id, offerIndex, response.response);
      } else {
        return markErrorToRequest(request.id, response.error.message).then(() => {
          throw new CustomError(response.error.message);
        });
      }
    }
  );
}

/**
 * Prepare the payload to send to Integrations
 *
 * @private
 * @param {QRequest} request
 * @param {QOffer} offer
 * @returns {Object}
 */
function createReservationPayload(request, offer) {
  let payload = {
    accountName: request.request.accountName,
    propertyCode: offer.propertyCode,
    checkin: offer.checkin,
    nights: offer.nights,
    rateId: offer.rateId,
    price: offer.roomRate,
    adults: offer.adults,
    infants: offer.infants,
    children: offer.children,
    notes: request.request.notes,
    rooms: offer.rooms,
    firstName: (request.guest && request.guest.firstName) || request.contact.firstName,
    lastName:
      (request.guest && request.guest.lastName) || request.contact.lastName || request.contact.name,
    email: (request.guest && request.guest.email) || request.contact.email,
    phone: request.contact.phone || null,
    externalId: request.id,
    serviceCodes: offer.serviceCodes,
    serviceDefs: offer.serviceDefs || null,
    serviceTotalRate: offer.serviceTotalRate || null,
  };

  if (request.attributes) {
    let attributes = request.attributes;
    attributes.language = request.language;
    payload.partnerData = JSON.stringify(attributes);
  }

  if (request.request.location) {
    payload.country = request.request.location;
  }

  if (request.contact.country) {
    payload.contactCountry = request.contact.country;
  }
  
  // Include flowType for createPaymentUrl
  payload.flowType = request.options && request.options.flowType; 

  return payload;
}

/**
 * Mark request with error
 *
 * @private
 * @param {string} requestId
 * @param {string} error
 * @param {ReservationResult} reservation
 * @returns {Promise}
 */
function markErrorToRequest(requestId, error) {
  logger.log('markErrorToRequest', 'Mark errors', { requestId, error });

  const updateExpressions = [
    `activities.failed = :dateNow`,
    'activities.#updated = :dateNow',
    `errors = :errors`,
  ];

  return ddb
    .update({
      TableName: process.env.REQUESTS_TABLE,
      Key: {
        id: requestId,
      },
      UpdateExpression: `SET ${updateExpressions.join(', ')}`,
      ExpressionAttributeNames: {
        '#updated': 'updated',
      },
      ExpressionAttributeValues: {
        ':dateNow': Date.now().toString(),
        ':errors': [error],
      },
    })
    .promise();
}

/**
 * Update Request's offers with reservation ids
 * and payment urls. Updates the state too.
 *
 * @typedef {Object} ReservationResult
 * @property {string} reservationId
 * @property {string} paymentUrl
 *
 * @param {string} requestId
 * @param {number} offerId
 * @param {ReservationResult} reservation
 * @returns {Promise}
 */
function updateOffer(requestId, offerId, reservation) {
  const attributeValues = {};
  attributeValues[`:reservationId`] = reservation.reservationId;
  attributeValues[`:paymentUrl`] = reservation.paymentUrl;

  const updateExpressions = [
    `offers[${offerId}].reservationId = :reservationId`,
    `offers[${offerId}].paymentUrl = :paymentUrl`,
  ];

  logger.log('updateOffer', `Update the request record for offer ${offerId}`, {
    updateExpressions,
  });

  return ddb
    .update({
      TableName: process.env.REQUESTS_TABLE,
      Key: {
        id: requestId,
      },
      UpdateExpression: `SET ${updateExpressions.join(', ')}`,
      ExpressionAttributeValues: attributeValues,
    })
    .promise();
}

/**
 * @private
 * @param {string} requestId - Request ID
 * @param {string} proposalUri - Proposal URL to fetch from S3
 * @return {Promise}
 */
function updateRequestStateToSent(requestId, proposalUri) {
  logger.log('updateRequestStateToSent', 'Update request state to sent', {
    requestId,
    proposalUri,
  });

  const updateExpressions = [
    '#state = :state',
    '#proposalUri = :proposalUri',
    'activities.#updated = :dateNow',
    'activities.#sent = :dateNow',
  ];

  return ddb
    .update({
      TableName: process.env.REQUESTS_TABLE,
      Key: {
        id: requestId,
      },
      UpdateExpression: 'SET ' + updateExpressions.join(', '),
      ExpressionAttributeValues: {
        ':state': 'sent',
        ':proposalUri': proposalUri,
        ':dateNow': Date.now().toString(),
      },
      ExpressionAttributeNames: {
        '#state': 'state',
        '#proposalUri': 'proposalUri',
        '#updated': 'updated',
        '#sent': 'sent',
      },
    })
    .promise();
}

/**
 * @private
 * @param {string} requestId
 * @param {string} html
 * @returns {Promise}
 */
function storeProposalToS3(requestId, html) {
  return s3
    .putObject({
      Bucket: process.env.PROPOSALS_BUCKET,
      Key: requestId,
      Body: html,
      CacheControl: 'no-cache,no-store',
      ContentType: 'text/html',
      ACL: 'public-read',
    })
    .promise();
}

module.exports = {
  handler: middlewares.init(publish),
  bookOffer,
  canPublish,
  getProposalUri,
  createReservationPayload,
};
