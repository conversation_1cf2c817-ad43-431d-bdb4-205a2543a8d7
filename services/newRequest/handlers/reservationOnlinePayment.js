const logger = require('@quotelier/logger');
const middlewares = require('../middlewares');
const requestsService = require('../lib/request');

const { handler: confirmRequest } = require('./confirmRequest');


/**
 * Special AWS-SNS handler wrapper.
 *
 * @param {{Records: SNSMessage[]}} event
 * @param {void} context
 * @param {Function} callback
 * @returns {void}
 */
function SNSHandler(event, context, callback) {
  logger.log('SNSHandler', 'Handle SNS records', event);
  Promise.all(
    event.Records.map(({ Sns }) => {
      return reservationOnlinePaymentHandler(JSON.parse(Sns.Message));
    })
  )
    .then(() => callback(null, null))
    .catch(err => callback(err, null));
}

/**
 * @param {QReservation} reservation
 * @returns {Promise<void>}
 */
async function reservationOnlinePaymentHandler(reservation) {
  logger.log('reservationOnlinePaymentHandler', 'Handle reservation online payment', { reservation });

  const { externalId } = reservation;

  const requestId = externalId.slice(1, externalId.length);

  const request = await requestsService.getRequestById(requestId);

  if (!request) {
    throw new Error(`Request not found: ${requestId}`);
  }

  if (!request.options) {
    throw new Error(`Request options missing: ${request}`);
  }

  if (request.options.flowType !== 'selectMany' && request.state === 'accepted') {
    await confirmRequest({ requestId });
  }

  return Promise.resolve();

}

module.exports = { SNSHandler: middlewares.init(SNSHandler) };
