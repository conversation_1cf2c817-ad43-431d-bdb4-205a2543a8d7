'use strict';
const Promise = require('bluebird');
const AWS = require('aws-sdk');
const ValidationError = require('./../errors/ValidationError');

const logger = require('@quotelier/logger');
const { emptySchedulerForRequest } = require('../lib/scheduler');
const { invokeLambda } = require('@quotelier/invokeLambda');
const errorHandler = require('./errorHandler');
const requestService = require('./../lib/request');
const middlewares = require('../middlewares');

const PURGE_RESERVATION_FUNCTION_NAME = process.env.PURGE_RESERVATION_FUNCTION_NAME;

const ddb = new AWS.DynamoDB.DocumentClient();

/**
 * @param {Object} event
 * @param {string} event.requestId
 * @param {Object} context
 * @param {Function} callback
 * @returns {void}
 */
function purgeRequest(event, context, callback) {
  logger.log('purgeRequest', 'get request with id', event.requestId);

  const session = {};
  const moveToState = requestService.REQUEST_STATE.SENT;
  const requestId = event.requestId;

  Promise.try(() => {
    if (!event.requestId) {
      throw new ValidationError('requestId is required');
    }
  })
    .then(() => requestService.getRequestById(event.requestId))
    .then(request => {
      session.request = request;
      return requestService.canMoveStateTo(request.state, requestService.REQUEST_STATE.DRAFT);
    })
    .then(can => {
      if (!can) {
        throw new Error(`Can not revert request ${requestId}`);
      } else {
        return purgeActiveReservations(session.request);
      }
    })
    .then(cancellations => revertRequestObject(requestId, cancellations))
    .then(() => requestService.updateRequestToState(event.requestId, moveToState))
    .then(() => emptySchedulerForRequest(event.requestId))
    .then(() =>
      callback(null, {
        success: true,
        response: null,
      })
    )
    .catch(err => errorHandler(err, callback));
}


/**
 *
 * @param {string} requestId
 * @param {number[]} cancellations
 * @returns {*}
 */
function revertRequestObject(requestId, cancellations) {
  const attributeNames = {
    '#state': 'state',
    '#markedToFollowUp': 'markedToFollowUp',
    '#updated': 'updated',
  };
  const attributeValues = {
    ':state': requestService.REQUEST_STATE.DRAFT,
    ':null': null,
    ':emptyArray': [],
    ':dateNow': Date.now().toString(),
  };

  let updateExpressions = [
    '#state = :state',
    '#markedToFollowUp = :null', // @deprecated
    'activities.#markedToFollowUp = :null',
    'activities.#updated = :dateNow',
    'activities.reverted = :dateNow',
    'acceptedOptionIds = :emptyArray',
  ];
  if (cancellations && cancellations.length) {
    cancellations.forEach(offerIndex => {
      updateExpressions.push(`offers[${offerIndex}].reservationId = :null`);
      updateExpressions.push(`offers[${offerIndex}].accepted = :null`);
    });
  }

  const updates = updateExpressions.join(', ');
  logger.log('revertRequestObject', 'revert and update request object', {
    requestId: requestId,
    updates: updates,
  });

  return ddb
    .update({
      TableName: process.env.REQUESTS_TABLE,
      Key: { id: requestId },
      UpdateExpression: `SET ${updates}`,
      ExpressionAttributeNames: attributeNames,
      ReturnValues: 'ALL_NEW',
      ExpressionAttributeValues: attributeValues,
    })
    .promise()
    .then(res => res.Attributes);
}

/**
 * @param {QPrimitiveRequest} request
 * @returns {Bluebird}
 */
function purgeActiveReservations(request) {
  logger.log('purgeActiveReservations', 'Purge active offer reservations of request', {
    id: request.id,
  });
  const cancellations = [];
  request.offers.forEach((offer, index) => {
    if (offer.reservationId) {
      const cancellation = invokeLambda(PURGE_RESERVATION_FUNCTION_NAME, {
        accountName: request.request.accountName,
        propertyCode: offer.propertyCode,
        reservationId: offer.reservationId,
      }).then(response => {
        if (response.success) {
          return index;
        } else {
          throw new Error(`Couldn't cancel reservation ${offer.reservationId}`);
        }
      });

      cancellations.push(cancellation);
    }
  });
  return Promise.all(cancellations);
}

module.exports = { handler: middlewares.init(purgeRequest) };
