defmodule Quotex.Accounts.AshAdminUserTest do
  use Quotex.DataCase

  alias Quotex.Accounts.User
  alias Quotex.Accounts.Account

  describe "AshAdmin User form configuration" do
    test "User resource has AshAdmin.Resource extension" do
      # Verify that the User resource includes the AshAdmin.Resource extension
      extensions = User.__ash_config__()[:extensions] || []
      assert AshAdmin.Resource in extensions
    end

    test "User admin configuration includes form field for account_id" do
      # This test verifies that the admin configuration is properly set up
      # We can't easily test the actual form rendering without a full Phoenix setup,
      # but we can verify the configuration exists
      admin_config = User.__ash_config__()[:admin]
      assert admin_config != nil
    end

    test "Account resource has label_field configured" do
      # Verify that Account has the label_field set to :name for dropdown display
      admin_config = Account.__ash_config__()[:admin]
      assert admin_config != nil
    end
  end

  describe "User-Account relationship in forms" do
    setup do
      # Create test accounts for dropdown testing
      {:ok, account1} = Ash.create(Account, %{name: "Test Account 1"})
      {:ok, account2} = Ash.create(Account, %{name: "Test Account 2"})
      {:ok, account3} = Ash.create(Account, %{name: "Test Account 3"})

      %{accounts: [account1, account2, account3]}
    end

    test "can create user with account relationship", %{accounts: [account1, _account2, _account3]} do
      # Test creating a user with an account
      {:ok, user} = Ash.create(User, %{
        email: "<EMAIL>",
        role: :operator,
        account_id: account1.id
      })

      assert user.account_id == account1.id
      
      # Load the relationship to verify it works
      user_with_account = Ash.load!(user, :account)
      assert user_with_account.account.id == account1.id
      assert to_string(user_with_account.account.name) == "Test Account 1"
    end

    test "can create user without account relationship" do
      # Test creating a user without an account (optional relationship)
      {:ok, user} = Ash.create(User, %{
        email: "<EMAIL>",
        role: :operator
      })

      assert user.account_id == nil
      
      # Load the relationship to verify it's nil
      user_with_account = Ash.load!(user, :account)
      assert user_with_account.account == nil
    end

    test "can update user to add account relationship", %{accounts: [account1, _account2, _account3]} do
      # Create user without account
      {:ok, user} = Ash.create(User, %{
        email: "<EMAIL>",
        role: :operator
      })
      assert user.account_id == nil

      # Update user to add account
      {:ok, updated_user} = Ash.update(user, %{account_id: account1.id})
      assert updated_user.account_id == account1.id

      # Verify the relationship
      user_with_account = Ash.load!(updated_user, :account)
      assert user_with_account.account.id == account1.id
    end

    test "can update user to change account relationship", %{accounts: [account1, account2, _account3]} do
      # Create user with account1
      {:ok, user} = Ash.create(User, %{
        email: "<EMAIL>",
        role: :operator,
        account_id: account1.id
      })
      assert user.account_id == account1.id

      # Update user to account2
      {:ok, updated_user} = Ash.update(user, %{account_id: account2.id})
      assert updated_user.account_id == account2.id

      # Verify the relationship changed
      user_with_account = Ash.load!(updated_user, :account)
      assert user_with_account.account.id == account2.id
      assert to_string(user_with_account.account.name) == "Test Account 2"
    end

    test "can update user to remove account relationship", %{accounts: [account1, _account2, _account3]} do
      # Create user with account
      {:ok, user} = Ash.create(User, %{
        email: "<EMAIL>",
        role: :operator,
        account_id: account1.id
      })
      assert user.account_id == account1.id

      # Update user to remove account
      {:ok, updated_user} = Ash.update(user, %{account_id: nil})
      assert updated_user.account_id == nil

      # Verify the relationship is removed
      user_with_account = Ash.load!(updated_user, :account)
      assert user_with_account.account == nil
    end

    test "account has_many users relationship works", %{accounts: [account1, _account2, _account3]} do
      # Create multiple users for the same account
      {:ok, user1} = Ash.create(User, %{
        email: "<EMAIL>",
        role: :operator,
        account_id: account1.id
      })
      
      {:ok, user2} = Ash.create(User, %{
        email: "<EMAIL>",
        role: :manager,
        account_id: account1.id
      })

      # Load the account with users
      account_with_users = Ash.load!(account1, :users)
      user_emails = Enum.map(account_with_users.users, &to_string(&1.email))
      
      assert length(account_with_users.users) == 2
      assert "<EMAIL>" in user_emails
      assert "<EMAIL>" in user_emails
    end
  end

  describe "Account dropdown data" do
    setup do
      # Create accounts with different names for testing dropdown display
      {:ok, account_a} = Ash.create(Account, %{name: "Alpha Account"})
      {:ok, account_b} = Ash.create(Account, %{name: "Beta Account"})
      {:ok, account_c} = Ash.create(Account, %{name: "Gamma Account"})

      %{accounts: [account_a, account_b, account_c]}
    end

    test "can load all accounts for dropdown", %{accounts: accounts} do
      # This simulates what AshAdmin would do to populate the dropdown
      all_accounts = Ash.read!(Account)
      
      # Verify we can load all accounts
      assert length(all_accounts) >= 3
      
      # Verify account names are accessible for display
      account_names = Enum.map(all_accounts, &to_string(&1.name))
      assert "Alpha Account" in account_names
      assert "Beta Account" in account_names
      assert "Gamma Account" in account_names
    end

    test "accounts are sorted by name for consistent dropdown order", %{accounts: _accounts} do
      # Load accounts and verify they can be sorted
      all_accounts = Ash.read!(Account)
      sorted_accounts = Enum.sort_by(all_accounts, &to_string(&1.name))
      
      sorted_names = Enum.map(sorted_accounts, &to_string(&1.name))
      assert sorted_names == Enum.sort(sorted_names)
    end

    test "account label_field configuration works for dropdown display" do
      {:ok, account} = Ash.create(Account, %{name: "Display Test Account"})
      
      # Verify the name field is accessible and properly formatted
      assert to_string(account.name) == "Display Test Account"
      
      # This simulates how AshAdmin would display the account in a dropdown
      display_text = to_string(account.name)
      assert display_text == "Display Test Account"
    end
  end
end
