defmodule Quotex.Accounts.UserTest do
  use Quotex.DataCase

  alias Quotex.Accounts.User
  alias Quotex.Accounts

  describe "role system" do
    test "roles/0 returns all available roles" do
      assert User.roles() == [:operator, :manager, :admin]
    end

    test "new users get default :operator role" do
      # Create a user directly using Ash
      assert {:ok, user} = Accounts.create(User, %{email: "<EMAIL>"})
      assert user.role == :operator
    end

    test "role validation accepts valid roles" do
      valid_roles = [:operator, :manager, :admin]

      Enum.each(valid_roles, fn role ->
        assert {:ok, user} = Accounts.create(User, %{email: "#{role}@example.com", role: role})
        assert user.role == role
      end)
    end

    test "role validation rejects invalid roles" do
      assert {:error, %Ash.Error.Invalid{}} =
               Accounts.create(User, %{email: "<EMAIL>", role: :invalid_role})
    end

    test "role validation rejects nil role" do
      assert {:error, %Ash.Error.Invalid{}} =
               Accounts.create(User, %{email: "<EMAIL>", role: nil})
    end

    test "has_role?/2 works with atom input" do
      {:ok, operator} = Accounts.create(User, %{email: "<EMAIL>", role: :operator})
      {:ok, manager} = Accounts.create(User, %{email: "<EMAIL>", role: :manager})
      {:ok, admin} = Accounts.create(User, %{email: "<EMAIL>", role: :admin})

      assert User.has_role?(operator, :operator) == true
      assert User.has_role?(operator, :manager) == false
      assert User.has_role?(operator, :admin) == false

      assert User.has_role?(manager, :operator) == false
      assert User.has_role?(manager, :manager) == true
      assert User.has_role?(manager, :admin) == false

      assert User.has_role?(admin, :operator) == false
      assert User.has_role?(admin, :manager) == false
      assert User.has_role?(admin, :admin) == true
    end

    test "has_role?/2 works with string input" do
      {:ok, operator} = Accounts.create(User, %{email: "<EMAIL>", role: :operator})
      {:ok, manager} = Accounts.create(User, %{email: "<EMAIL>", role: :manager})
      {:ok, admin} = Accounts.create(User, %{email: "<EMAIL>", role: :admin})

      assert User.has_role?(operator, "operator") == true
      assert User.has_role?(operator, "manager") == false
      assert User.has_role?(operator, "admin") == false

      assert User.has_role?(manager, "operator") == false
      assert User.has_role?(manager, "manager") == true
      assert User.has_role?(manager, "admin") == false

      assert User.has_role?(admin, "operator") == false
      assert User.has_role?(admin, "manager") == false
      assert User.has_role?(admin, "admin") == true
    end

    test "has_role?/2 handles invalid string input gracefully" do
      {:ok, user} = Accounts.create(User, %{email: "<EMAIL>", role: :operator})

      assert User.has_role?(user, "invalid_role") == false
      assert User.has_role?(user, "nonexistent") == false
    end

    test "is_admin?/1 returns true for admin users" do
      {:ok, admin} = Accounts.create(User, %{email: "<EMAIL>", role: :admin})
      assert User.is_admin?(admin) == true
    end

    test "is_admin?/1 returns false for non-admin users" do
      {:ok, operator} = Accounts.create(User, %{email: "<EMAIL>", role: :operator})
      {:ok, manager} = Accounts.create(User, %{email: "<EMAIL>", role: :manager})

      assert User.is_admin?(operator) == false
      assert User.is_admin?(manager) == false
    end
  end

  describe "user-account relationship" do
    test "user can belong to an account" do
      # Create an account first
      {:ok, account} = Accounts.create(Quotex.Accounts.Account, %{name: "Test Account"})

      # Create a user with account relationship
      {:ok, user} = Accounts.create(User, %{email: "<EMAIL>", account_id: account.id})

      # Load the user with account relationship
      user_with_account = Accounts.load!(user, :account)
      assert user_with_account.account.id == account.id
      assert user_with_account.account.name == "Test Account"
    end

    test "user can exist without an account" do
      {:ok, user} = Accounts.create(User, %{email: "<EMAIL>"})
      assert user.account_id == nil
    end

    test "account can have many users" do
      # Create an account
      {:ok, account} = Accounts.create(Quotex.Accounts.Account, %{name: "Test Account"})

      # Create multiple users for the account
      {:ok, user1} = Accounts.create(User, %{email: "<EMAIL>", account_id: account.id})
      {:ok, user2} = Accounts.create(User, %{email: "<EMAIL>", account_id: account.id})

      # Load the account with users
      account_with_users = Accounts.load!(account, :users)
      user_emails = Enum.map(account_with_users.users, & &1.email)

      assert length(account_with_users.users) == 2
      assert "<EMAIL>" in user_emails
      assert "<EMAIL>" in user_emails
    end
  end

  describe "magic link authentication with roles" do
    test "magic link sign in creates user with default :operator role" do
      # This test simulates the magic link authentication flow
      # We'll use the sign_in_with_magic_link action which should create a user with default role

      # First, we need to request a magic link (this creates a token)
      assert {:ok, _} =
               Accounts.invoke(User, :request_magic_link, %{email: "<EMAIL>"})

      # In a real scenario, the user would click the magic link and we'd get a token
      # For testing, we'll need to extract the token from the database or mock it
      # Since this is complex to test without the full email flow, we'll test the upsert behavior directly

      # Test the upsert behavior that happens during magic link sign in
      email = "<EMAIL>"

      # First sign in should create user with default role
      {:ok, user} = Accounts.create(User, %{email: email})
      assert user.role == :operator
      assert user.email == email

      # Subsequent "sign ins" (upserts) should not change the role
      # Simulates upsert
      {:ok, updated_user} = Accounts.update(user, %{email: email})
      assert updated_user.role == :operator
    end

    test "existing users keep their roles during magic link authentication" do
      # Create a user with manager role
      {:ok, user} = Accounts.create(User, %{email: "<EMAIL>", role: :manager})
      assert user.role == :manager

      # Simulate what happens during magic link authentication (upsert)
      # The upsert should not change the existing role
      {:ok, updated_user} = Accounts.update(user, %{email: "<EMAIL>"})
      assert updated_user.role == :manager
    end
  end
end
